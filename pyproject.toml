[project]
name = "hksi-server"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "accelerate>=1.6.0",
    "aiohttp-catcher>=0.3.2",
    "aiohttp[speedups]>=3.11.18",
    "aiortc>=1.11.0",
    "av>=14.3.0",
    "dlib>=19.24.8",
    "einops>=0.8.1",
    "insightface>=0.7.3",
    "mediapipe>=0.10.21",
    "msvc-runtime>=14.42.34433 ; sys_platform == 'win32'",
    "onnx>=1.17.0",
    "onnxruntime>=1.21.1",
    "opencv-python>=*********",
    "pandas>=2.2.3",
    "pillow>=11.2.1",
    "pyampd>=0.0.1",
    "pymongo>=4.12.1",
    "python-dotenv>=1.1.0",
    "sentencepiece>=0.2.0",
    "timm>=1.0.15",
    "torch>=2.7.0",
    "torchvision>=0.22.0",
    "transformers>=4.51.3",
]

# Pin PyTorch packages to a specific CUDA version
# Guide at https://docs.astral.sh/uv/concepts/indexes/#pinning-a-package-to-an-index
[tool.uv.sources]
torch = [{ index = "pytorch-cu126", marker = "sys_platform != 'darwin'" }]
torchvision = [{ index = "pytorch-cu126", marker = "sys_platform != 'darwin'" }]
torchaudio = [{ index = "pytorch-cu126", marker = "sys_platform != 'darwin'" }]

# Specify the PyTorch index with CUDA 12.6
# URL found at https://pytorch.org/get-started/locally/
[[tool.uv.index]]
name = "pytorch-cu126"
url = "https://download.pytorch.org/whl/cu126"
explicit = true

[tool.ruff.lint]
select = ["D", "I"]

ignore = ["D100", "D101"]

[tool.ruff.lint.per-file-ignores]
"!models.py" = ["D"]

[tool.ruff.lint.pydocstyle]
convention = "numpy"
